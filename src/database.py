"""
Database Manager for PassChanger
Handles local SQLite database operations with encryption
"""

import sqlite3
import asyncio
import logging
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import aiosqlite

# from .security_utils import SecurityManager  # Will be imported when needed

logger = logging.getLogger("PassChanger.Database")

class DatabaseManager:
    def __init__(self, config: dict):
        self.config = config
        self.db_path = config['database']['path']
        self.security_manager = None

    async def initialize(self):
        """Initialize database and create tables"""
        try:
            # Ensure data directory exists (skip for in-memory database)
            if self.db_path != ':memory:':
                Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)

            # Create database tables
            await self._create_tables()

            logger.info(f"Database initialized at {self.db_path}")

        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise

    async def _create_tables(self):
        """Create database tables"""
        async with aiosqlite.connect(self.db_path) as db:
            # Accounts table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS accounts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    email TEXT NOT NULL,
                    username TEXT,
                    category TEXT DEFAULT 'standard',
                    status TEXT DEFAULT 'unknown',
                    has_2fa BOOLEAN DEFAULT FALSE,
                    last_password_change TIMESTAMP,
                    last_check TIMESTAMP,
                    risk_level TEXT DEFAULT 'unknown',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Leak detections table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS leak_detections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    account_id INTEGER,
                    source TEXT NOT NULL,
                    detection_type TEXT NOT NULL,
                    risk_level TEXT NOT NULL,
                    confidence REAL DEFAULT 0.0,
                    description TEXT,
                    raw_data TEXT,
                    ai_analysis TEXT,
                    status TEXT DEFAULT 'new',
                    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved_at TIMESTAMP,
                    FOREIGN KEY (account_id) REFERENCES accounts (id)
                )
            """)

            # Security events table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS security_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    description TEXT,
                    account_id INTEGER,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (account_id) REFERENCES accounts (id)
                )
            """)

            # Password change history
            await db.execute("""
                CREATE TABLE IF NOT EXISTS password_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    account_id INTEGER NOT NULL,
                    change_type TEXT NOT NULL,
                    success BOOLEAN DEFAULT FALSE,
                    error_message TEXT,
                    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (account_id) REFERENCES accounts (id)
                )
            """)

            # Settings table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            await db.commit()

    async def add_account(self, account_data: Dict[str, Any]) -> int:
        """Add a new account to monitor"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                INSERT INTO accounts (name, email, username, category, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                account_data['name'],
                account_data['email'],
                account_data.get('username'),
                account_data.get('category', 'standard'),
                datetime.now()
            ))

            account_id = cursor.lastrowid
            await db.commit()

            # Log security event
            await self.log_security_event(
                'account_added',
                'info',
                f"Account '{account_data['name']}' added to monitoring",
                account_id
            )

            logger.info(f"Added account: {account_data['name']}")
            return account_id

    async def get_all_accounts(self) -> List[Dict[str, Any]]:
        """Get all monitored accounts"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row

            cursor = await db.execute("""
                SELECT * FROM accounts ORDER BY category, name
            """)

            rows = await cursor.fetchall()
            return [dict(row) for row in rows]

    async def get_account_by_id(self, account_id: int) -> Optional[Dict[str, Any]]:
        """Get account by ID"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row

            cursor = await db.execute("""
                SELECT * FROM accounts WHERE id = ?
            """, (account_id,))

            row = await cursor.fetchone()
            return dict(row) if row else None

    async def update_account_status(self, account_id: int, status: str, risk_level: str = None):
        """Update account status and risk level"""
        async with aiosqlite.connect(self.db_path) as db:
            if risk_level:
                await db.execute("""
                    UPDATE accounts
                    SET status = ?, risk_level = ?, last_check = ?, updated_at = ?
                    WHERE id = ?
                """, (status, risk_level, datetime.now(), datetime.now(), account_id))
            else:
                await db.execute("""
                    UPDATE accounts
                    SET status = ?, last_check = ?, updated_at = ?
                    WHERE id = ?
                """, (status, datetime.now(), datetime.now(), account_id))

            await db.commit()

    async def record_leak_detection(self, detection_data: Dict[str, Any]) -> int:
        """Record a leak detection"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                INSERT INTO leak_detections
                (account_id, source, detection_type, risk_level, confidence,
                 description, raw_data, ai_analysis)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                detection_data.get('account_id'),
                detection_data['source'],
                detection_data['detection_type'],
                detection_data['risk_level'],
                detection_data.get('confidence', 0.0),
                detection_data.get('description'),
                detection_data.get('raw_data'),
                json.dumps(detection_data.get('ai_analysis', {}))
            ))

            detection_id = cursor.lastrowid
            await db.commit()

            # Log security event
            await self.log_security_event(
                'leak_detected',
                detection_data['risk_level'],
                detection_data.get('description', 'Potential leak detected'),
                detection_data.get('account_id')
            )

            logger.warning(f"Recorded leak detection: {detection_data['description']}")
            return detection_id

    async def get_recent_detections(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get recent leak detections"""
        since_date = datetime.now() - timedelta(days=days)

        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row

            cursor = await db.execute("""
                SELECT ld.*, a.name as account_name, a.email as account_email
                FROM leak_detections ld
                LEFT JOIN accounts a ON ld.account_id = a.id
                WHERE ld.detected_at >= ?
                ORDER BY ld.detected_at DESC
            """, (since_date,))

            rows = await cursor.fetchall()
            detections = []

            for row in rows:
                detection = dict(row)
                if detection['ai_analysis']:
                    try:
                        detection['ai_analysis'] = json.loads(detection['ai_analysis'])
                    except json.JSONDecodeError:
                        detection['ai_analysis'] = {}
                detections.append(detection)

            return detections

    async def get_account_detections(self, account_id: int, include_resolved: bool = False) -> List[Dict[str, Any]]:
        """Get all leak detections for a specific account"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row

            # Build query based on whether to include resolved detections
            if include_resolved:
                query = """
                    SELECT ld.*, a.name as account_name, a.email as account_email
                    FROM leak_detections ld
                    LEFT JOIN accounts a ON ld.account_id = a.id
                    WHERE ld.account_id = ?
                    ORDER BY ld.detected_at DESC
                """
            else:
                query = """
                    SELECT ld.*, a.name as account_name, a.email as account_email
                    FROM leak_detections ld
                    LEFT JOIN accounts a ON ld.account_id = a.id
                    WHERE ld.account_id = ? AND (ld.status != 'resolved' OR ld.status IS NULL)
                    ORDER BY ld.detected_at DESC
                """

            cursor = await db.execute(query, (account_id,))
            rows = await cursor.fetchall()
            detections = []

            for row in rows:
                detection = dict(row)
                if detection['ai_analysis']:
                    try:
                        detection['ai_analysis'] = json.loads(detection['ai_analysis'])
                    except json.JSONDecodeError:
                        detection['ai_analysis'] = {}
                detections.append(detection)

            return detections

    async def log_security_event(self, event_type: str, severity: str,
                                description: str, account_id: int = None,
                                metadata: Dict[str, Any] = None):
        """Log a security event"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO security_events
                (event_type, severity, description, account_id, metadata)
                VALUES (?, ?, ?, ?, ?)
            """, (
                event_type,
                severity,
                description,
                account_id,
                json.dumps(metadata) if metadata else None
            ))

            await db.commit()

    async def get_security_events(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent security events"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row

            cursor = await db.execute("""
                SELECT se.*, a.name as account_name
                FROM security_events se
                LEFT JOIN accounts a ON se.account_id = a.id
                ORDER BY se.created_at DESC
                LIMIT ?
            """, (limit,))

            rows = await cursor.fetchall()
            events = []

            for row in rows:
                event = dict(row)
                if event['metadata']:
                    try:
                        event['metadata'] = json.loads(event['metadata'])
                    except json.JSONDecodeError:
                        event['metadata'] = {}
                events.append(event)

            return events

    async def record_password_change(self, account_id: int, change_type: str,
                                   success: bool, error_message: str = None):
        """Record a password change attempt"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO password_history
                (account_id, change_type, success, error_message)
                VALUES (?, ?, ?, ?)
            """, (account_id, change_type, success, error_message))

            # Update account's last password change if successful
            if success:
                await db.execute("""
                    UPDATE accounts
                    SET last_password_change = ?, updated_at = ?
                    WHERE id = ?
                """, (datetime.now(), datetime.now(), account_id))

            await db.commit()

    async def get_setting(self, key: str) -> Optional[str]:
        """Get a setting value"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT value FROM settings WHERE key = ?
            """, (key,))

            row = await cursor.fetchone()
            return row[0] if row else None

    async def set_setting(self, key: str, value: str):
        """Set a setting value"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT OR REPLACE INTO settings (key, value, updated_at)
                VALUES (?, ?, ?)
            """, (key, value, datetime.now()))

            await db.commit()

    async def close(self):
        """Close database connections"""
        # SQLite connections are closed automatically with context managers
        logger.info("Database connections closed")
